// 代码生成工具函数

// 工具函数：转换为PascalCase
export const toPascalCase = (str) => {
  return str.replace(/[^a-zA-Z0-9]/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('');
};

// 工具函数：转换为camelCase
export const toCamelCase = (str) => {
  if (!str || typeof str !== 'string' || str.trim() === '') {
    return '';
  }
  const pascal = toPascalCase(str);
  return pascal.charAt(0).toLowerCase() + pascal.slice(1);
};

// 生成字段名的统一函数
const generateFieldName = (element) => {
  const placeholder = element.props?.placeholder;
  let fieldName = '';

  if (placeholder && placeholder.trim() !== '') {
    fieldName = toCamelCase(placeholder);
  }

  // 如果转换后的字段名为空，使用默认字段名
  if (!fieldName) {
    // 将ID转换为有效的JavaScript标识符
    const cleanId = String(element.id).replace(/[^a-zA-Z0-9]/g, '');
    fieldName = `field${cleanId}`;
  }

  return fieldName;
};

// 生成表单数据对象
export const generateFormData = (elements) => {
  const dataFields = [];
  elements.forEach(element => {
    if (element.type === 'ElInput' && element.id) {
      const fieldName = generateFieldName(element);
      dataFields.push(`  ${fieldName}: ''`);
    }
  });
  return dataFields.length > 0 ? dataFields.join(',\n') : '';
};

// 生成模板代码
export const generateTemplate = (elements) => {
  const templateParts = [];

  elements.forEach(element => {
    const styleAttr = `position: absolute; left: ${element.position?.x || 0}px; top: ${element.position?.y || 0}px; width: ${element.size?.width || 120}px; min-height: ${element.size?.height || 40}px;`;

    switch (element.type) {
      case 'ElInput':
        const fieldName = generateFieldName(element);
        const placeholder = element.props?.placeholder;

        const inputTemplate = [
          '        <div',
          `          :style="{ ${formatStyleObject(styleAttr)} }"`,
          '        >',
          '          <el-input',
          `            v-model="formData.${fieldName}"`,
          `            placeholder="${placeholder || ''}"`,
          '            clearable',
          '          />',
          '        </div>'
        ].join('\n');
        templateParts.push(inputTemplate);
        break;

      case 'ElButton':
        const buttonTemplate = [
          '        <div',
          `          :style="{ ${formatStyleObject(styleAttr)} }"`,
          '        >',
          '          <el-button',
          '            type="primary"',
          '          >',
          `            ${element.props?.content || '按钮'}`,
          '          </el-button>',
          '        </div>'
        ].join('\n');
        templateParts.push(buttonTemplate);
        break;

      case 'ElText':
        const textTemplate = [
          '        <div',
          `          :style="{ ${formatStyleObject(styleAttr)} }"`,
          '        >',
          '          <div class="form-text">',
          `            ${element.props?.content || element.props?.text || '文本内容'}`,
          '          </div>',
          '        </div>'
        ].join('\n');
        templateParts.push(textTemplate);
        break;
    }
  });

  return templateParts.join('\n');
};

// 辅助函数：格式化样式对象为Vue样式绑定格式
const formatStyleObject = (styleString) => {
  // 将CSS样式字符串转换为Vue样式对象格式
  const styles = styleString.split(';').filter(s => s.trim());
  const styleProps = styles.map(style => {
    const [property, value] = style.split(':').map(s => s.trim());
    if (!property || !value) return '';

    // 转换CSS属性名为camelCase
    const camelProperty = property.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
    return `${camelProperty}: '${value}'`;
  }).filter(s => s);

  return styleProps.join(', ');
};

// 生成样式代码
export const generateStyle = () => {
  return `.form-wrapper {
  max-width: 800px;
  margin: 0 auto;
  padding: 30px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-title {
  text-align: center;
  color: #333;
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: 600;
}

.form-description {
  text-align: center;
  color: #666;
  margin-bottom: 30px;
  font-size: 16px;
  line-height: 1.5;
}

.form-content {
  position: relative;
  min-height: 400px;
  border: 1px solid #e1e8ed;
  border-radius: 4px;
  background-color: #fafafa;
  margin-bottom: 30px;
  padding: 20px;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #41b883;
  box-shadow: 0 0 0 2px rgba(65, 184, 131, 0.2);
}

.form-button {
  padding: 10px 20px;
  background-color: #41b883;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.form-button:hover {
  background-color: #35495e;
}

.form-text {
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  padding: 8px 0;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e1e8ed;
}

.submit-btn {
  padding: 12px 30px;
  background-color: #67c23a;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: background-color 0.2s;
}

.submit-btn:hover {
  background-color: #5daf34;
}

.reset-btn {
  padding: 12px 30px;
  background-color: white;
  color: #333;
  border: 1px solid #e1e8ed;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
}

.reset-btn:hover {
  background-color: #fafbfc;
}`;
};

// 生成Vue组件代码
export const generateVueComponent = (form, elements) => {
  const componentName = toPascalCase(form.name);
  const formDataFields = generateFormData(elements);
  const templateContent = generateTemplate(elements);
  const styleContent = generateStyle();

  // 使用数组拼接而不是字符串拼接来避免Vue编译器问题
  const codeLines = [];

  // Template部分 - 符合Vue SFC标准格式
  codeLines.push('<template>');
  codeLines.push(`  <div class="${componentName.toLowerCase()}-container">`);
  codeLines.push('    <div class="form-wrapper">');
  codeLines.push(`      <h1 class="form-title">`);
  codeLines.push(`        ${form.name}`);
  codeLines.push('      </h1>');

  if (form.description) {
    codeLines.push('      <p class="form-description">');
    codeLines.push(`        ${form.description}`);
    codeLines.push('      </p>');
  }

  codeLines.push('');
  codeLines.push('      <form');
  codeLines.push('        @submit.prevent="handleSubmit"');
  codeLines.push('        class="form-content"');
  codeLines.push('      >');

  // 添加生成的表单内容
  if (templateContent.trim()) {
    codeLines.push(templateContent);
    codeLines.push('');
  }

  codeLines.push('        <div class="form-actions">');
  codeLines.push('          <el-button');
  codeLines.push('            type="primary"');
  codeLines.push('            native-type="submit"');
  codeLines.push('          >');
  codeLines.push('            提交');
  codeLines.push('          </el-button>');
  codeLines.push('          <el-button');
  codeLines.push('            @click="resetForm"');
  codeLines.push('          >');
  codeLines.push('            重置');
  codeLines.push('          </el-button>');
  codeLines.push('        </div>');
  codeLines.push('      </form>');
  codeLines.push('    </div>');
  codeLines.push('  </div>');
  codeLines.push('</template>');
  codeLines.push('');

  // Script部分 - 符合Composition API标准格式
  codeLines.push('<script setup>');
  codeLines.push("import { reactive } from 'vue'");
  codeLines.push('');
  codeLines.push('// 表单数据');
  codeLines.push('const formData = reactive({');
  if (formDataFields.trim()) {
    codeLines.push(formDataFields);
  }
  codeLines.push('})');
  codeLines.push('');
  codeLines.push('// 提交表单');
  codeLines.push('const handleSubmit = () => {');
  codeLines.push("  console.log('表单数据:', formData)");
  codeLines.push('  // 在这里添加你的提交逻辑');
  codeLines.push("  alert('表单提交成功！')");
  codeLines.push('}');
  codeLines.push('');
  codeLines.push('// 重置表单');
  codeLines.push('const resetForm = () => {');
  codeLines.push('  Object.keys(formData).forEach(key => {');
  codeLines.push("    formData[key] = ''");
  codeLines.push('  })');
  codeLines.push('}');
  codeLines.push('</script>');
  codeLines.push('');

  // Style部分 - 符合CSS标准格式
  codeLines.push('<style scoped>');
  codeLines.push(styleContent);
  codeLines.push('</style>');

  return codeLines.join('\n');
};
