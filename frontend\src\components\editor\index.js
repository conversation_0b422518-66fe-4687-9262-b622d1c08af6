import EditorButton from '@/components/editor/ElButton.vue';
import EditorInput from '@/components/editor/ElInput.vue';
import EditorText from '@/components/editor/ElText.vue';

// 组件列表
export const components = {
  ElButton: EditorButton,
  ElInput: EditorInput,
  ElText: EditorText
};

// 注册所有组件，使用不同的名称避免与Element Plus冲突
export function registerEditorComponents(app) {
  Object.entries(components).forEach(([name, component]) => {
    // 使用Editor前缀避免与Element Plus组件冲突
    const editorComponentName = `Editor${name.replace('El', '')}`;
    app.component(editorComponentName, component);
  });
}

export default {
  install(app) {
    registerEditorComponents(app);
  }
};
