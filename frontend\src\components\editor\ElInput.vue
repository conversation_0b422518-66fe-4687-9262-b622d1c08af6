<template>
  <el-input
    :placeholder="placeholder"
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    :clearable="clearable"
  />
</template>

<script setup>
defineProps({
  placeholder: {
    type: String,
    default: "请输入内容",
  },
  modelValue: {
    type: String,
    default: "",
  },
  clearable: {
    type: Boolean,
    default: false,
  },
});

defineEmits(["update:modelValue"]);
</script>


