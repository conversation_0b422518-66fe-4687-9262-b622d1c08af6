const API_BASE_URL = 'http://localhost:9999';

// 通用请求函数
async function request(url, options = {}) {
  const response = await fetch(`${API_BASE_URL}${url}`, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(error || `HTTP error! status: ${response.status}`);
  }

  return response.json();
}

// 表单API
export const formsApi = {
  // 获取所有表单
  async getAll() {
    return request('/forms');
  },

  // 根据ID获取表单
  async getById(id) {
    return request(`/forms/${id}`);
  },

  // 创建表单
  async create(formData) {
    return request('/forms', {
      method: 'POST',
      body: JSON.stringify(formData),
    });
  },

  // 更新表单
  async update(id, formData) {
    return request(`/forms/${id}`, {
      method: 'PUT',
      body: JSON.stringify(formData),
    });
  },

  // 删除表单（逻辑删除）
  async delete(id) {
    return request(`/forms/${id}`, {
      method: 'DELETE',
    });
  },

  // 获取已删除的表单
  async getDeleted() {
    return request('/forms/deleted');
  },

  // 恢复删除的表单
  async restore(id) {
    return request(`/forms/${id}/restore`, {
      method: 'POST',
    });
  },

  // 永久删除表单
  async permanentDelete(id) {
    return request(`/forms/${id}/permanent`, {
      method: 'DELETE',
    });
  },

  // 复制表单
  async duplicate(id) {
    return request(`/forms/${id}/duplicate`, {
      method: 'POST',
    });
  },
};
