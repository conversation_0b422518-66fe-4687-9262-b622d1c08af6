import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddDeletedAtToForm1703000000000 implements MigrationInterface {
  name = 'AddDeletedAtToForm1703000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'form',
      new TableColumn({
        name: 'deletedAt',
        type: 'datetime',
        isNullable: true,
        default: null,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('form', 'deletedAt');
  }
}
