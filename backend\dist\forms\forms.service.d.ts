import { Repository } from 'typeorm';
import { Form } from '../entities/form.entity';
export interface CreateFormDto {
    name: string;
    description?: string;
    content: string;
    status?: string;
}
export interface UpdateFormDto {
    name?: string;
    description?: string;
    content?: string;
    status?: string;
}
export declare class FormsService {
    private formsRepository;
    constructor(formsRepository: Repository<Form>);
    findAll(): Promise<Form[]>;
    findOne(id: number): Promise<Form | null>;
    findDeleted(): Promise<Form[]>;
    findOneDeleted(id: number): Promise<Form | null>;
    create(createFormDto: CreateFormDto): Promise<Form>;
    update(id: number, updateFormDto: UpdateFormDto): Promise<Form | null>;
    remove(id: number): Promise<void>;
    restore(id: number): Promise<Form | null>;
    permanentDelete(id: number): Promise<void>;
    duplicate(id: number): Promise<Form | null>;
}
