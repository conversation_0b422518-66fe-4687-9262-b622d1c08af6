<template>
  <div class="forms-container">
    <div class="forms-header">
      <h1>表单管理</h1>
      <div class="header-actions">
        <router-link to="/editor" class="create-button">
          <span class="icon">➕</span>
          创建新表单
        </router-link>
      </div>
    </div>

    <div class="forms-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <p>加载中...</p>
      </div>

      <!-- 表单列表 -->
      <div v-else-if="forms.length > 0" class="forms-grid">
        <div v-for="form in forms" :key="form.id" class="form-card">
          <!-- 卡片头部 -->
          <div class="form-card-header">
            <div class="form-title-wrapper">
              <h3 class="form-title">{{ form.name }}</h3>
              <span class="form-status" :class="form.status">
                {{ getStatusText(form.status) }}
              </span>
            </div>
            <button
              @click="previewForm(form)"
              class="quick-preview-btn"
              title="快速预览"
            >
              <el-icon><View /></el-icon>
            </button>
          </div>

          <!-- 卡片内容 -->
          <div class="form-card-body">
            <p class="form-description">
              {{ form.description || "暂无描述" }}
            </p>
            <div class="form-meta">
              <div class="meta-item">
                <span class="meta-label">创建:</span>
                <span class="meta-value">{{ formatDate(form.createdAt) }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">更新:</span>
                <span class="meta-value">{{ formatDate(form.updatedAt) }}</span>
              </div>
            </div>
          </div>

          <!-- 卡片操作区 -->
          <div class="form-card-actions">
            <div class="primary-actions">
              <el-button
                size="small"
                type="primary"
                @click="editForm(form.id)"
                :icon="Edit"
                class="action-btn primary-btn"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                type="info"
                @click="previewForm(form)"
                :icon="View"
                class="action-btn"
              >
                预览
              </el-button>
            </div>
            <div class="secondary-actions">
              <el-button
                size="small"
                type="success"
                @click="viewCode(form)"
                :icon="Document"
                class="action-btn"
                plain
              >
                代码
              </el-button>
              <el-button
                size="small"
                type="warning"
                @click="duplicateForm(form.id)"
                :icon="CopyDocument"
                class="action-btn"
                plain
              >
                复制
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="deleteForm(form.id)"
                :icon="Delete"
                class="action-btn"
                plain
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-icon">📝</div>
        <h3>还没有表单</h3>
        <p>创建您的第一个表单开始使用低代码编辑器</p>
        <router-link to="/editor" class="create-button">
          创建新表单
        </router-link>
      </div>
    </div>

    <!-- 代码查看模态框 -->
    <el-dialog
      v-model="showCodeModal"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      class="code-dialog"
      width="85%"
      top="5vh"
      :show-close="false"
      :destroy-on-close="true"
    >
      <template #header>
        <div class="code-dialog-header">
          <div class="dialog-title">
            <div class="title-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="title-content">
              <h3>{{ currentForm?.name }}</h3>
              <span class="subtitle">Vue组件代码</span>
            </div>
          </div>
          <div class="dialog-actions">
            <el-button
              type="primary"
              @click="copyCode"
              :icon="DocumentCopy"
              :loading="copied"
              size="small"
            >
              {{ copied ? "已复制!" : "复制代码" }}
            </el-button>
            <el-button
              type="success"
              @click="downloadCode"
              :icon="Download"
              size="small"
            >
              下载文件
            </el-button>
            <el-button
              @click="showCodeModal = false"
              :icon="Close"
              size="small"
              circle
              class="close-btn"
            >
            </el-button>
          </div>
        </div>
      </template>

      <div class="code-viewer">
        <div class="code-header">
          <div class="file-info">
            <span class="file-name"
              >{{ toPascalCase(currentForm?.name || "Component") }}.vue</span
            >
            <span class="file-size"
              >{{ (generatedCode.length / 1024).toFixed(1) }}KB</span
            >
          </div>
          <div class="code-stats">
            <span class="lines-count"
              >{{ generatedCode.split("\n").length }} 行</span
            >
          </div>
        </div>
        <div class="code-content">
          <pre><code class="language-vue">{{ generatedCode }}</code></pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { formsApi } from "@/api/forms.js";
import { generateVueComponent, toPascalCase } from "@/utils/codeGenerator.js";
import { Message, Confirm } from "@/utils/message.js";
import {
  View,
  Document,
  Edit,
  Delete,
  CopyDocument,
  DocumentCopy,
  Download,
  Close,
} from "@element-plus/icons-vue";

const router = useRouter();

// 响应式数据
const forms = ref([]);
const loading = ref(true);

// 代码查看相关
const showCodeModal = ref(false);
const currentForm = ref(null);
const generatedCode = ref("");
const copied = ref(false);

// 获取表单列表
const loadForms = async () => {
  loading.value = true;
  try {
    forms.value = await formsApi.getAll();
  } catch (error) {
    console.error("加载表单列表失败:", error);
    Message.error("加载表单列表失败: " + error.message);
  } finally {
    loading.value = false;
  }
};

// 预览表单
const previewForm = (form) => {
  try {
    // 解析表单内容
    const elements = JSON.parse(form.content);

    // 准备预览数据
    const previewData = {
      elements: elements,
      formInfo: {
        name: form.name,
        description: form.description || "",
      },
    };

    // 将数据存储到 sessionStorage
    sessionStorage.setItem("previewData", JSON.stringify(previewData));

    // 在新窗口中打开预览页面
    const previewUrl = router.resolve({ name: "Preview" }).href;
    window.open(previewUrl, "_blank");
  } catch (error) {
    console.error("预览表单失败:", error);
    Message.error("预览表单失败: 表单数据格式错误");
  }
};

// 查看代码
const viewCode = (form) => {
  try {
    currentForm.value = form;
    const elements = JSON.parse(form.content);
    generatedCode.value = generateVueComponent(form, elements);
    showCodeModal.value = true;
  } catch (error) {
    console.error("生成代码失败:", error);
    Message.error("生成代码失败: 表单数据格式错误");
  }
};

// 复制代码
const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(generatedCode.value);
    copied.value = true;
    Message.success("代码已复制到剪贴板");
    setTimeout(() => {
      copied.value = false;
    }, 2000);
  } catch (error) {
    console.error("复制失败:", error);
    // 降级方案：创建临时文本区域
    try {
      const textArea = document.createElement("textarea");
      textArea.value = generatedCode.value;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand("copy");
      document.body.removeChild(textArea);
      copied.value = true;
      Message.success("代码已复制到剪贴板");
      setTimeout(() => {
        copied.value = false;
      }, 2000);
    } catch (fallbackError) {
      Message.error("复制失败，请手动复制");
    }
  }
};

// 下载代码文件
const downloadCode = () => {
  const componentName = toPascalCase(currentForm.value.name);
  const blob = new Blob([generatedCode.value], { type: "text/plain" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `${componentName}.vue`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// 编辑表单
const editForm = (formId) => {
  router.push(`/editor?id=${formId}`);
};

// 复制表单
const duplicateForm = async (formId) => {
  try {
    await formsApi.duplicate(formId);
    Message.success("表单复制成功！");
    loadForms(); // 重新加载列表
  } catch (error) {
    console.error("复制表单失败:", error);
    Message.error("复制表单失败: " + error.message);
  }
};

// 删除表单
const deleteForm = async (formId) => {
  const confirmed = await Confirm.delete();
  if (!confirmed) {
    return;
  }

  try {
    await formsApi.delete(formId);
    Message.success("表单删除成功！");
    loadForms(); // 重新加载列表
  } catch (error) {
    console.error("删除表单失败:", error);
    Message.error("删除表单失败: " + error.message);
  }
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    draft: "草稿",
    published: "已发布",
    archived: "已归档",
  };
  return statusMap[status] || status;
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 组件挂载时加载数据
onMounted(() => {
  loadForms();
});
</script>

<style lang="less" scoped>
// 变量定义
@primary-color: #41b883;
@secondary-color: #35495e;
@background-color: #fafbfc;
@white: #fff;
@border-color: #e1e8ed;
@border-light: #eee;
@text-color: #333;
@text-muted: #666;
@text-light: #999;
@warning-color: #f39c12;
@warning-hover: #e67e22;
@danger-color: #e74c3c;
@danger-hover: #c0392b;
@info-color: #3498db;
@info-hover: #2980b9;
@shadow-light: rgba(0, 0, 0, 0.1);

// 状态颜色
@draft-bg: #f0f9ff;
@draft-color: #0369a1;
@published-bg: #f0fdf4;
@published-color: #166534;
@archived-bg: #fafafa;
@archived-color: #525252;

.forms-container {
  height: 100%;
  width: 100%;
  padding: 20px;
  overflow-y: auto;
  background-color: @background-color;
}

.forms-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding: 30px 0 25px;
  border-bottom: 2px solid #f0f0f0;
  background: linear-gradient(
    135deg,
    rgba(65, 184, 131, 0.05) 0%,
    rgba(53, 73, 94, 0.05) 100%
  );
  border-radius: 12px 12px 0 0;
  margin: -20px -20px 40px -20px;
  padding: 30px 40px 25px;

  h1 {
    color: @text-color;
    margin: 0;
    font-size: 32px;
    font-weight: 700;
    letter-spacing: -0.02em;
    background: linear-gradient(
      135deg,
      @primary-color 0%,
      @secondary-color 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.create-button {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 12px 24px;
  background: linear-gradient(135deg, @primary-color 0%, #35a085 100%);
  color: @white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(65, 184, 131, 0.3);
  border: none;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(65, 184, 131, 0.4);
    background: linear-gradient(135deg, #35a085 0%, @secondary-color 100%);
  }

  .icon {
    font-size: 16px;
  }
}

.loading {
  text-align: center;
  padding: 40px;
  color: @text-muted;
}

.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
  padding: 20px 0;
}

.form-card {
  border: 1px solid @border-color;
  border-radius: 12px;
  padding: 0;
  background: @white;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  &:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
    border-color: @primary-color;
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f5f5f5;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);

    .form-title-wrapper {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex: 1;
    }

    .form-title {
      margin: 0;
      color: @text-color;
      font-size: 20px;
      font-weight: 600;
      line-height: 1.3;
      letter-spacing: -0.02em;
    }
  }

  &-body {
    padding: 20px 24px;
  }

  &-actions {
    padding: 16px 24px 20px;
    background-color: #fafbfc;
    border-top: 1px solid #f0f0f0;

    .primary-actions {
      display: flex;
      gap: 12px;
      margin-bottom: 12px;
    }

    .secondary-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
    }
  }
}

.form-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid transparent;

  &.draft {
    background-color: @draft-bg;
    color: @draft-color;
    border-color: rgba(3, 105, 161, 0.2);
  }

  &.published {
    background-color: @published-bg;
    color: @published-color;
    border-color: rgba(22, 101, 52, 0.2);
  }

  &.archived {
    background-color: @archived-bg;
    color: @archived-color;
    border-color: rgba(82, 82, 82, 0.2);
  }
}

.form-description {
  color: @text-muted;
  margin-bottom: 20px;
  line-height: 1.6;
  font-size: 14px;
  min-height: 42px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.form-meta {
  display: flex;
  gap: 16px;

  .meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;

    .meta-label {
      color: @text-light;
      font-weight: 500;
    }

    .meta-value {
      color: @text-muted;
      font-weight: 400;
    }
  }
}

.action-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &.primary-btn {
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(65, 184, 131, 0.3);

    &:hover {
      box-shadow: 0 4px 12px rgba(65, 184, 131, 0.4);
    }
  }
}

.primary-actions {
  .action-btn {
    flex: 1;
    min-height: 36px;
    font-size: 14px;
  }
}

.secondary-actions {
  .action-btn {
    min-width: 70px;
    font-size: 12px;

    &:hover {
      transform: translateY(-1px);
    }
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;

  h3 {
    color: @text-color;
    margin-bottom: 10px;
  }

  p {
    color: @text-muted;
    margin-bottom: 30px;
  }
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.quick-preview-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

  &:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  }

  &:active {
    transform: scale(0.95);
  }

  .el-icon {
    font-size: 18px;
  }
}

// 代码弹窗样式
:deep(.code-dialog) {
  .el-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
  }

  .el-dialog {
    margin: 0 !important;
    height: 90vh;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
  }

  .el-dialog__header {
    padding: 0;
    margin: 0;
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0;
  }

  .el-dialog__body {
    padding: 0;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }
}

.code-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
  flex-shrink: 0;

  .dialog-title {
    display: flex;
    align-items: center;
    gap: 12px;

    .title-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, @primary-color 0%, #35a085 100%);
      border-radius: 10px;
      color: white;

      .el-icon {
        font-size: 20px;
      }
    }

    .title-content {
      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: @text-color;
        line-height: 1.2;
      }

      .subtitle {
        font-size: 14px;
        color: @text-muted;
        font-weight: 400;
      }
    }
  }

  .dialog-actions {
    display: flex;
    gap: 8px;
    align-items: center;

    .close-btn {
      background: #f1f5f9;
      border: 1px solid #e2e8f0;
      color: @text-muted;

      &:hover {
        background: #e2e8f0;
        color: @text-color;
      }
    }
  }
}

.code-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #fafbfc;
  min-height: 0;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px;
  flex-shrink: 0;

  .file-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .file-name {
      font-weight: 600;
      color: @text-color;
      font-family: "Fira Code", "Monaco", "Consolas", monospace;
    }

    .file-size {
      color: @text-muted;
      background: #e5e7eb;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
    }
  }

  .code-stats {
    .lines-count {
      color: @text-muted;
      font-weight: 500;
    }
  }
}

.code-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  background: #ffffff;
  position: relative;
  min-height: 0;
  max-height: 100%;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;

    &:hover {
      background: #94a3b8;
    }
  }

  pre {
    margin: 0;
    padding: 24px;
    background: transparent;
    font-family: "Fira Code", "SF Mono", "Monaco", "Inconsolata", "Roboto Mono",
      "Consolas", monospace;
    font-size: 14px;
    line-height: 1.6;
    color: #1e293b;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
    overflow: visible;
  }

  code {
    background: transparent;
    padding: 0;
    border-radius: 0;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    display: block;
  }
}
</style>
