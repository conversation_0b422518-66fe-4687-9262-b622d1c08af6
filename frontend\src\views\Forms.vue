<template>
  <div class="forms-container">
    <div class="forms-header">
      <h1>表单管理</h1>
      <div class="header-actions">
        <router-link to="/editor" class="create-button">
          <span class="icon">➕</span>
          创建新表单
        </router-link>
      </div>
    </div>

    <div class="forms-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading">
        <p>加载中...</p>
      </div>

      <!-- 表单列表 -->
      <div v-else-if="forms.length > 0" class="forms-grid">
        <div v-for="form in forms" :key="form.id" class="form-card">
          <div class="form-card-header">
            <div class="form-title-section">
              <h3>{{ form.name }}</h3>
              <button
                @click="previewForm(form)"
                class="quick-preview-btn"
                title="快速预览"
              >
                👁️
              </button>
            </div>
            <span class="form-status" :class="form.status">
              {{ getStatusText(form.status) }}
            </span>
          </div>

          <div class="form-card-body">
            <p class="form-description">
              {{ form.description || "暂无描述" }}
            </p>
            <div class="form-meta">
              <span>创建时间: {{ formatDate(form.createdAt) }}</span>
              <span>更新时间: {{ formatDate(form.updatedAt) }}</span>
            </div>
          </div>

          <div class="form-card-actions">
            <el-button
              size="small"
              type="info"
              @click="previewForm(form)"
              :icon="View"
            >
              预览
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="viewCode(form)"
              :icon="Document"
            >
              代码
            </el-button>
            <el-button
              size="small"
              type="success"
              @click="editForm(form.id)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-button
              size="small"
              type="warning"
              @click="duplicateForm(form.id)"
              :icon="CopyDocument"
            >
              复制
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="deleteForm(form.id)"
              :icon="Delete"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-icon">📝</div>
        <h3>还没有表单</h3>
        <p>创建您的第一个表单开始使用低代码编辑器</p>
        <router-link to="/editor" class="create-button">
          创建新表单
        </router-link>
      </div>
    </div>

    <!-- 代码查看模态框 -->
    <el-dialog
      v-model="showCodeModal"
      :title="`${currentForm?.name} - Vue组件代码`"
      width="90%"
      :close-on-click-modal="false"
      class="code-dialog"
    >
      <div class="code-actions">
        <el-button
          type="primary"
          @click="copyCode"
          :icon="DocumentCopy"
          :loading="copied"
        >
          {{ copied ? "已复制!" : "复制代码" }}
        </el-button>
        <el-button type="success" @click="downloadCode" :icon="Download">
          下载文件
        </el-button>
      </div>
      <div class="code-content">
        <pre><code>{{ generatedCode }}</code></pre>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { formsApi } from "@/api/forms.js";
import { generateVueComponent, toPascalCase } from "@/utils/codeGenerator.js";
import { Message, Confirm } from "@/utils/message.js";
import {
  View,
  Document,
  Edit,
  Delete,
  CopyDocument,
  DocumentCopy,
  Download,
} from "@element-plus/icons-vue";

const router = useRouter();

// 响应式数据
const forms = ref([]);
const loading = ref(true);

// 代码查看相关
const showCodeModal = ref(false);
const currentForm = ref(null);
const generatedCode = ref("");
const copied = ref(false);

// 获取表单列表
const loadForms = async () => {
  loading.value = true;
  try {
    forms.value = await formsApi.getAll();
  } catch (error) {
    console.error("加载表单列表失败:", error);
    Message.error("加载表单列表失败: " + error.message);
  } finally {
    loading.value = false;
  }
};

// 预览表单
const previewForm = (form) => {
  try {
    // 解析表单内容
    const elements = JSON.parse(form.content);

    // 准备预览数据
    const previewData = {
      elements: elements,
      formInfo: {
        name: form.name,
        description: form.description || "",
      },
    };

    // 将数据存储到 sessionStorage
    sessionStorage.setItem("previewData", JSON.stringify(previewData));

    // 在新窗口中打开预览页面
    const previewUrl = router.resolve({ name: "Preview" }).href;
    window.open(previewUrl, "_blank");
  } catch (error) {
    console.error("预览表单失败:", error);
    Message.error("预览表单失败: 表单数据格式错误");
  }
};

// 查看代码
const viewCode = (form) => {
  try {
    currentForm.value = form;
    const elements = JSON.parse(form.content);
    generatedCode.value = generateVueComponent(form, elements);
    showCodeModal.value = true;
  } catch (error) {
    console.error("生成代码失败:", error);
    Message.error("生成代码失败: 表单数据格式错误");
  }
};

// 复制代码
const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(generatedCode.value);
    copied.value = true;
    Message.success("代码已复制到剪贴板");
    setTimeout(() => {
      copied.value = false;
    }, 2000);
  } catch (error) {
    console.error("复制失败:", error);
    // 降级方案：创建临时文本区域
    try {
      const textArea = document.createElement("textarea");
      textArea.value = generatedCode.value;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand("copy");
      document.body.removeChild(textArea);
      copied.value = true;
      Message.success("代码已复制到剪贴板");
      setTimeout(() => {
        copied.value = false;
      }, 2000);
    } catch (fallbackError) {
      Message.error("复制失败，请手动复制");
    }
  }
};

// 下载代码文件
const downloadCode = () => {
  const componentName = toPascalCase(currentForm.value.name);
  const blob = new Blob([generatedCode.value], { type: "text/plain" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `${componentName}.vue`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// 编辑表单
const editForm = (formId) => {
  router.push(`/editor?id=${formId}`);
};

// 复制表单
const duplicateForm = async (formId) => {
  try {
    await formsApi.duplicate(formId);
    Message.success("表单复制成功！");
    loadForms(); // 重新加载列表
  } catch (error) {
    console.error("复制表单失败:", error);
    Message.error("复制表单失败: " + error.message);
  }
};

// 删除表单
const deleteForm = async (formId) => {
  const confirmed = await Confirm.delete();
  if (!confirmed) {
    return;
  }

  try {
    await formsApi.delete(formId);
    Message.success("表单删除成功！");
    loadForms(); // 重新加载列表
  } catch (error) {
    console.error("删除表单失败:", error);
    Message.error("删除表单失败: " + error.message);
  }
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    draft: "草稿",
    published: "已发布",
    archived: "已归档",
  };
  return statusMap[status] || status;
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 组件挂载时加载数据
onMounted(() => {
  loadForms();
});
</script>

<style lang="less" scoped>
// 变量定义
@primary-color: #41b883;
@secondary-color: #35495e;
@background-color: #fafbfc;
@white: #fff;
@border-color: #e1e8ed;
@border-light: #eee;
@text-color: #333;
@text-muted: #666;
@text-light: #999;
@warning-color: #f39c12;
@warning-hover: #e67e22;
@danger-color: #e74c3c;
@danger-hover: #c0392b;
@info-color: #3498db;
@info-hover: #2980b9;
@shadow-light: rgba(0, 0, 0, 0.1);

// 状态颜色
@draft-bg: #f0f9ff;
@draft-color: #0369a1;
@published-bg: #f0fdf4;
@published-color: #166534;
@archived-bg: #fafafa;
@archived-color: #525252;

.forms-container {
  height: 100%;
  width: 100%;
  padding: 20px;
  overflow-y: auto;
  background-color: @background-color;
}

.forms-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid @border-light;

  h1 {
    color: @primary-color;
    margin: 0;
  }
}

.create-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: @primary-color;
  color: @white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: background-color 0.2s;

  &:hover {
    background-color: @secondary-color;
  }
}

.loading {
  text-align: center;
  padding: 40px;
  color: @text-muted;
}

.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  padding: 20px 0;
}

.form-card {
  border: 1px solid @border-color;
  border-radius: 8px;
  padding: 20px;
  background: @white;
  transition: box-shadow 0.2s, transform 0.2s;

  &:hover {
    box-shadow: 0 4px 12px @shadow-light;
    transform: translateY(-2px);
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;

    .form-title-section {
      display: flex;
      align-items: center;
      gap: 10px;
      flex: 1;

      h3 {
        margin: 0;
        color: @text-color;
        font-size: 18px;
        line-height: 1.4;
        flex: 1;
      }
    }
  }

  &-body {
    margin-bottom: 20px;
  }

  &-actions {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
  }
}

.form-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.draft {
    background-color: @draft-bg;
    color: @draft-color;
  }

  &.published {
    background-color: @published-bg;
    color: @published-color;
  }

  &.archived {
    background-color: @archived-bg;
    color: @archived-color;
  }
}

.form-description {
  color: @text-muted;
  margin-bottom: 15px;
  line-height: 1.5;
}

.form-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: @text-light;
}

.action-btn {
  flex: 1;
  min-width: 60px;
  padding: 8px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s;
  text-align: center;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.preview-btn {
  background-color: @info-color;
  color: @white;

  &:hover {
    background-color: @info-hover;
  }
}

.code-btn {
  background-color: #9b59b6;
  color: @white;

  &:hover {
    background-color: #8e44ad;
  }
}

.edit-btn {
  background-color: @primary-color;
  color: @white;

  &:hover {
    background-color: @secondary-color;
  }
}

.duplicate-btn {
  background-color: @warning-color;
  color: @white;

  &:hover {
    background-color: @warning-hover;
  }
}

.delete-btn {
  background-color: @danger-color;
  color: @white;

  &:hover {
    background-color: @danger-hover;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;

  h3 {
    color: @text-color;
    margin-bottom: 10px;
  }

  p {
    color: @text-muted;
    margin-bottom: 30px;
  }
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.quick-preview-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
  opacity: 0.7;

  &:hover {
    opacity: 1;
    background-color: @info-color;
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }
}

// 代码模态框样式
.code-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.code-modal {
  background: @white;
  border-radius: 8px;
  width: 90%;
  max-width: 1000px;
  max-height: 90%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.code-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid @border-color;

  h3 {
    margin: 0;
    color: @text-color;
    font-size: 18px;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: @text-muted;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      background-color: @background-color;
      color: @text-color;
    }
  }
}

.code-modal-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.code-actions {
  display: flex;
  gap: 10px;
  padding: 16px 24px;
  border-bottom: 1px solid @border-color;
  background-color: @background-color;

  .copy-btn,
  .download-btn {
    padding: 8px 16px;
    border: 1px solid @border-color;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
  }

  .copy-btn {
    background-color: @primary-color;
    color: @white;
    border-color: @primary-color;

    &:hover {
      background-color: @secondary-color;
    }
  }

  .download-btn {
    background-color: @white;
    color: @text-color;

    &:hover {
      background-color: @background-color;
    }
  }
}

.code-content {
  flex: 1;
  margin: 0;
  padding: 24px;
  background-color: #f8f9fa;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 13px;
  line-height: 1.5;
  overflow: auto;
  white-space: pre-wrap;
  word-wrap: break-word;

  code {
    color: #333;
  }
}
</style>
