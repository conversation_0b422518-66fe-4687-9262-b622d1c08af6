<template>
  <div class="trash-container">
    <!-- 页面头部 -->
    <div class="trash-header">
      <h1>回收站</h1>
      <div class="header-actions">
        <el-button @click="loadDeletedForms" :icon="Refresh" type="primary">
          刷新
        </el-button>
        <router-link to="/forms" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回表单列表
        </router-link>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      正在加载已删除的表单...
    </div>

    <!-- 表单列表 -->
    <div v-else-if="deletedForms.length > 0" class="forms-grid">
      <div
        v-for="form in deletedForms"
        :key="form.id"
        class="form-card deleted"
      >
        <!-- 卡片头部 -->
        <div class="form-card-header">
          <div class="form-title-wrapper">
            <h3 class="form-title">{{ form.name }}</h3>
            <span class="deleted-badge">已删除</span>
          </div>
          <div class="deleted-time">
            删除时间: {{ formatDate(form.deletedAt) }}
          </div>
        </div>

        <!-- 卡片内容 -->
        <div class="form-card-body">
          <p class="form-description">
            {{ form.description || "暂无描述" }}
          </p>
          <div class="form-meta">
            <div class="meta-item">
              <span class="meta-label">创建:</span>
              <span class="meta-value">{{ formatDate(form.createdAt) }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">更新:</span>
              <span class="meta-value">{{ formatDate(form.updatedAt) }}</span>
            </div>
          </div>
        </div>

        <!-- 卡片操作区 -->
        <div class="form-card-actions">
          <div class="restore-actions">
            <el-button
              size="small"
              type="success"
              @click="restoreForm(form.id)"
              :icon="RefreshRight"
              class="action-btn"
            >
              恢复
            </el-button>
          </div>
          <div class="danger-actions">
            <el-button
              size="small"
              type="danger"
              @click="permanentDeleteForm(form.id)"
              :icon="Delete"
              class="action-btn"
              plain
            >
              永久删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">🗑️</div>
      <h3>回收站为空</h3>
      <p>没有已删除的表单</p>
      <router-link to="/forms" class="back-button"> 返回表单列表 </router-link>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { formsApi } from "@/api/forms.js";
import { Message, Confirm } from "@/utils/message.js";
import {
  Refresh,
  ArrowLeft,
  Loading,
  RefreshRight,
  Delete,
} from "@element-plus/icons-vue";

const router = useRouter();

// 响应式数据
const deletedForms = ref([]);
const loading = ref(true);

// 获取已删除的表单列表
const loadDeletedForms = async () => {
  loading.value = true;
  try {
    deletedForms.value = await formsApi.getDeleted();
  } catch (error) {
    console.error("加载已删除表单失败:", error);
    Message.error("加载已删除表单失败: " + error.message);
  } finally {
    loading.value = false;
  }
};

// 恢复表单
const restoreForm = async (formId) => {
  const confirmed = await Confirm.show(
    "恢复后表单将重新出现在表单列表中",
    "确定要恢复这个表单吗？",
    "info"
  );
  if (!confirmed) {
    return;
  }

  try {
    await formsApi.restore(formId);
    Message.success("表单恢复成功！");
    loadDeletedForms(); // 重新加载列表
  } catch (error) {
    console.error("恢复表单失败:", error);
    Message.error("恢复表单失败: " + error.message);
  }
};

// 永久删除表单
const permanentDeleteForm = async (formId) => {
  const confirmed = await Confirm.delete(
    "此操作不可撤销，表单将被彻底删除！确定要永久删除这个表单吗？",
    "永久删除确认"
  );
  if (!confirmed) {
    return;
  }

  try {
    await formsApi.permanentDelete(formId);
    Message.success("表单已永久删除！");
    loadDeletedForms(); // 重新加载列表
  } catch (error) {
    console.error("永久删除表单失败:", error);
    Message.error("永久删除表单失败: " + error.message);
  }
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 组件挂载时加载数据
onMounted(() => {
  loadDeletedForms();
});
</script>

<style lang="less" scoped>
// 变量定义
@primary-color: #41b883;
@secondary-color: #35495e;
@background-color: #fafbfc;
@white: #fff;
@border-color: #e1e8ed;
@text-color: #333;
@text-muted: #666;
@text-light: #999;
@danger-color: #e74c3c;
@success-color: #27ae60;

.trash-container {
  height: 100%;
  width: 100%;
  padding: 20px;
  overflow-y: auto;
  background-color: @background-color;
}

.trash-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding: 30px 0 25px;
  border-bottom: 2px solid #f0f0f0;
  background: linear-gradient(
    135deg,
    rgba(231, 76, 60, 0.05) 0%,
    rgba(192, 57, 43, 0.05) 100%
  );
  border-radius: 12px 12px 0 0;
  margin: -20px -20px 40px -20px;
  padding: 30px 40px 25px;

  h1 {
    color: @text-color;
    margin: 0;
    font-size: 32px;
    font-weight: 700;
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, @danger-color 0%, #c0392b 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: @secondary-color;
  color: @white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: background-color 0.2s;

  &:hover {
    background-color: #2c3e50;
  }
}

.loading {
  text-align: center;
  padding: 40px;
  color: @text-muted;
  font-size: 16px;

  .el-icon {
    font-size: 24px;
    margin-right: 8px;
  }
}

.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
  padding: 20px 0;
}

.form-card {
  border: 1px solid @border-color;
  border-radius: 12px;
  padding: 0;
  background: @white;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  &.deleted {
    border-color: @danger-color;
    background: linear-gradient(
      135deg,
      rgba(231, 76, 60, 0.02) 0%,
      rgba(255, 255, 255, 1) 100%
    );
  }

  &:hover {
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.15);
    transform: translateY(-4px);
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f5f5f5;
    background: linear-gradient(135deg, #fef7f7 0%, #fdf2f2 100%);

    .form-title-wrapper {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex: 1;
    }

    .form-title {
      margin: 0;
      color: @text-color;
      font-size: 20px;
      font-weight: 600;
      line-height: 1.3;
      letter-spacing: -0.02em;
    }

    .deleted-badge {
      padding: 4px 8px;
      background: @danger-color;
      color: white;
      font-size: 11px;
      font-weight: 600;
      border-radius: 12px;
      align-self: flex-start;
    }

    .deleted-time {
      font-size: 12px;
      color: @text-light;
      text-align: right;
    }
  }

  &-body {
    padding: 20px 24px;
  }

  &-actions {
    padding: 16px 24px 20px;
    background-color: #fafbfc;
    border-top: 1px solid #f0f0f0;

    .restore-actions {
      display: flex;
      gap: 12px;
      margin-bottom: 12px;
    }

    .danger-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-end;
    }
  }
}

.form-description {
  color: @text-muted;
  margin-bottom: 20px;
  line-height: 1.6;
  font-size: 14px;
  min-height: 42px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.form-meta {
  display: flex;
  gap: 16px;

  .meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;

    .meta-label {
      color: @text-light;
      font-weight: 500;
    }

    .meta-value {
      color: @text-muted;
      font-weight: 400;
    }
  }
}

.action-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: @text-muted;

  .empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
  }

  h3 {
    margin: 0 0 10px 0;
    color: @text-color;
    font-size: 24px;
  }

  p {
    margin: 0 0 30px 0;
    font-size: 16px;
  }
}
</style>
