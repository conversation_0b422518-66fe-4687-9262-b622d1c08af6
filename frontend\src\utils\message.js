import { ElMessage, ElMessageBox } from 'element-plus'

/**
 * 统一的消息提示工具
 */
export const Message = {
  /**
   * 成功消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长，默认3000ms
   */
  success(message, duration = 3000) {
    ElMessage({
      message,
      type: 'success',
      duration,
      showClose: true
    })
  },

  /**
   * 错误消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长，默认3000ms
   */
  error(message, duration = 3000) {
    ElMessage({
      message,
      type: 'error',
      duration,
      showClose: true
    })
  },

  /**
   * 警告消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长，默认3000ms
   */
  warning(message, duration = 3000) {
    ElMessage({
      message,
      type: 'warning',
      duration,
      showClose: true
    })
  },

  /**
   * 信息消息
   * @param {string} message - 消息内容
   * @param {number} duration - 显示时长，默认3000ms
   */
  info(message, duration = 3000) {
    ElMessage({
      message,
      type: 'info',
      duration,
      showClose: true
    })
  }
}

/**
 * 统一的确认框工具
 */
export const Confirm = {
  /**
   * 确认删除
   * @param {string} message - 确认消息，默认为"确定要删除吗？此操作不可恢复。"
   * @param {string} title - 标题，默认为"删除确认"
   * @returns {Promise<boolean>} - 用户确认返回true，取消返回false
   */
  async delete(message = '确定要删除吗？此操作不可恢复。', title = '删除确认') {
    try {
      await ElMessageBox.confirm(message, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
      return true
    } catch {
      return false
    }
  },

  /**
   * 通用确认框
   * @param {string} message - 确认消息
   * @param {string} title - 标题，默认为"确认"
   * @param {string} type - 类型，默认为"warning"
   * @returns {Promise<boolean>} - 用户确认返回true，取消返回false
   */
  async show(message, title = '确认', type = 'warning') {
    try {
      await ElMessageBox.confirm(message, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type,
        center: true
      })
      return true
    } catch {
      return false
    }
  },

  /**
   * 清空确认
   * @param {string} message - 确认消息，默认为"确定要清空吗？"
   * @param {string} title - 标题，默认为"清空确认"
   * @returns {Promise<boolean>} - 用户确认返回true，取消返回false
   */
  async clear(message = '确定要清空吗？', title = '清空确认') {
    try {
      await ElMessageBox.confirm(message, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
      return true
    } catch {
      return false
    }
  }
}

/**
 * 输入框
 */
export const Prompt = {
  /**
   * 输入框
   * @param {string} message - 提示消息
   * @param {string} title - 标题
   * @param {string} inputValue - 默认值
   * @returns {Promise<string|null>} - 用户输入的值，取消返回null
   */
  async input(message, title = '输入', inputValue = '') {
    try {
      const { value } = await ElMessageBox.prompt(message, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue,
        center: true
      })
      return value
    } catch {
      return null
    }
  }
}
