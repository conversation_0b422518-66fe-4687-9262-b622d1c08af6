# 低代码表单构建器

一个基于 Vue 3 + Vite + Element Plus 的低代码表单构建器，支持拖拽式组件设计、实时预览、表单管理等功能。

## 项目截图

![项目截图](./public/images/screenshot.png)

## 功能特性

### 🎨 **可视化编辑器**

- 拖拽式组件设计
- 实时预览功能
- 网格对齐系统
- 组件属性配置

### 📦 **丰富的组件库**

- 按钮组件 (ElButton)
- 输入框组件 (ElInput)
- 下拉选择组件 (ElSelect)
- 文本组件 (ElText)
- 富文本组件 (ElRichText)

### 💾 **表单管理**

- 表单保存与加载
- 表单列表管理
- 表单复制功能
- 逻辑删除与回收站

### 🔧 **代码生成**

- 自动生成 Vue 组件代码
- 支持代码预览和下载
- 符合 ESLint 规范

## 技术栈

- **前端框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **UI 组件库**: Element Plus
- **CSS 预处理器**: Less
- **路由管理**: Vue Router
- **后端框架**: Nest.js
- **数据库**: MySQL

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

## 项目结构

```
frontend/
├── src/
│   ├── components/          # 组件目录
│   │   └── editor/         # 编辑器组件
│   ├── views/              # 页面组件
│   ├── utils/              # 工具函数
│   ├── api/                # API 接口
│   └── router/             # 路由配置
├── public/                 # 静态资源
└── README.md
```

## 开发说明

本项目使用 Vue 3 的 `<script setup>` 语法，详细信息请参考 [Vue 3 文档](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup)。

更多 IDE 支持信息请查看 [Vue 工具链指南](https://vuejs.org/guide/scaling-up/tooling.html#ide-support)。
