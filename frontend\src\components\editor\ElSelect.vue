<template>
  <el-select
    :placeholder="placeholder"
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    :clearable="clearable"
    style="width: 100%"
  >
    <el-option
      v-for="option in options"
      :key="option.value"
      :label="option.label"
      :value="option.value"
    />
  </el-select>
</template>

<script setup>
defineProps({
  placeholder: {
    type: String,
    default: "请选择",
  },
  modelValue: {
    type: [String, Number],
    default: "",
  },
  clearable: {
    type: Boolean,
    default: true,
  },
  options: {
    type: Array,
    default: () => [
      { label: "选项1", value: "option1" },
      { label: "选项2", value: "option2" },
      { label: "选项3", value: "option3" },
    ],
  },
});

defineEmits(["update:modelValue"]);
</script>
