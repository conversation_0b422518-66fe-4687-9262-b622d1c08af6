"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddDeletedAtToForm20241219160000 = void 0;
const typeorm_1 = require("typeorm");
class AddDeletedAtToForm20241219160000 {
    name = 'AddDeletedAtToForm20241219160000';
    async up(queryRunner) {
        await queryRunner.addColumn('form', new typeorm_1.TableColumn({
            name: 'deletedAt',
            type: 'datetime',
            isNullable: true,
            default: null,
            comment: '逻辑删除时间戳',
        }));
        await queryRunner.query(`CREATE INDEX IDX_form_deletedAt ON form (deletedAt)`);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX IDX_form_deletedAt ON form`);
        await queryRunner.dropColumn('form', 'deletedAt');
    }
}
exports.AddDeletedAtToForm20241219160000 = AddDeletedAtToForm20241219160000;
//# sourceMappingURL=20241219160000-AddDeletedAtToForm.js.map