"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddDeletedAtToForm1703000000000 = void 0;
const typeorm_1 = require("typeorm");
class AddDeletedAtToForm1703000000000 {
    name = 'AddDeletedAtToForm1703000000000';
    async up(queryRunner) {
        await queryRunner.addColumn('form', new typeorm_1.TableColumn({
            name: 'deletedAt',
            type: 'datetime',
            isNullable: true,
            default: null,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('form', 'deletedAt');
    }
}
exports.AddDeletedAtToForm1703000000000 = AddDeletedAtToForm1703000000000;
//# sourceMappingURL=1703000000000-AddDeletedAtToForm.js.map