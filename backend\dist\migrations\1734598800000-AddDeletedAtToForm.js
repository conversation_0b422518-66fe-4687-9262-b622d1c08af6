"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddDeletedAtToForm1734598800000 = void 0;
const typeorm_1 = require("typeorm");
class AddDeletedAtToForm1734598800000 {
    name = 'AddDeletedAtToForm1734598800000';
    async up(queryRunner) {
        await queryRunner.addColumn('form', new typeorm_1.TableColumn({
            name: 'deletedAt',
            type: 'datetime',
            isNullable: true,
            default: null,
            comment: '逻辑删除时间戳',
        }));
        await queryRunner.query(`CREATE INDEX IDX_form_deletedAt ON form (deletedAt)`);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX IDX_form_deletedAt ON form`);
        await queryRunner.dropColumn('form', 'deletedAt');
    }
}
exports.AddDeletedAtToForm1734598800000 = AddDeletedAtToForm1734598800000;
//# sourceMappingURL=1734598800000-AddDeletedAtToForm.js.map